import pandas as pd
import requests
import time
import json
import math
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Tuple, Optional
import threading

def dms_to_decimal(degrees, minutes, seconds):
    """将度分秒转换为十进制度数"""
    return degrees + minutes/60 + seconds/3600

class GasStationCoordinateQuery:
    """加油站坐标查询和对比工具"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://maps.googleapis.com/maps/api/geocode/json"
        self.results = []

        # 台湾的地理范围
        self.taiwan_bounds = {
            'min_lat': 21.5, 'max_lat': 25.5,
            'min_lng': 119.5, 'max_lng': 122.5
        }
        
    def geocode_address(self, address: str, region: str = "tw") -> Optional[Dict]:
        """使用Google Geocoding API查询地址坐标"""
        params = {
            'address': address,
            'key': self.api_key,
            'language': 'zh-TW',
            'region': region
        }

        try:
            response = requests.get(self.base_url, params=params)
            response.raise_for_status()
            data = response.json()

            if data['status'] == 'OK' and data['results']:
                location = data['results'][0]['geometry']['location']
                return {
                    'lat': location['lat'],
                    'lng': location['lng'],
                    'formatted_address': data['results'][0]['formatted_address'],
                    'status': 'SUCCESS'
                }
            else:
                return {
                    'lat': None,
                    'lng': None,
                    'formatted_address': None,
                    'status': f"ERROR: {data['status']}"
                }
        except Exception as e:
            return {
                'lat': None,
                'lng': None,
                'formatted_address': None,
                'status': f"EXCEPTION: {str(e)}"
            }

    def geocode_single_query(self, query_data: Dict) -> Dict:
        """单个查询任务，用于并发执行"""
        index = query_data['index']
        query = query_data['query']
        query_type = query_data['type']  # 'query1' or 'query2'

        result = self.geocode_address(query)

        return {
            'index': index,
            'type': query_type,
            'query': query,
            'result': result
        }
    
    def calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """计算两点间的距离（米）"""
        if None in [lat1, lng1, lat2, lng2]:
            return float('inf')
            
        # 使用Haversine公式计算距离
        R = 6371000  # 地球半径（米）
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lng = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lng/2) * math.sin(delta_lng/2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def process_excel_file_concurrent(self, file_path: str, max_workers: int = 100) -> List[Dict]:
        """并发处理Excel文件，进行两次查询并对比"""
        print(f"正在读取Excel文件: {file_path}")

        try:
            df = pd.read_excel(file_path)
            print(f"文件读取成功，共 {len(df)} 行数据")
            print(f"列名: {df.columns.tolist()}")

            # 显示前几行数据以确认结构
            print("\n前5行数据:")
            print(df.head())

        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return []

        # 准备所有查询任务
        query_tasks = []
        row_data = []

        for index, row in df.iterrows():
            # 获取各列数据
            col2 = str(row.iloc[1]) if pd.notna(row.iloc[1]) else ""  # 縣市
            col3 = str(row.iloc[2]) if pd.notna(row.iloc[2]) else ""  # 鄉鎮市區
            col4 = str(row.iloc[3]) if pd.notna(row.iloc[3]) else ""  # 品牌
            col5 = str(row.iloc[4]) if pd.notna(row.iloc[4]) else ""  # 加油站名稱
            col6 = str(row.iloc[5]) if pd.notna(row.iloc[5]) else ""  # 地址

            # 第一次查询：组合第2-5列（縣市+鄉鎮市區+品牌+加油站名稱）
            query1 = f"{col2} {col3} {col4} {col5}".strip()

            # 第二次查询：第6列地址
            query2 = col6.strip()

            # 保存行数据
            row_data.append({
                'index': index,
                'county': col2,
                'district': col3,
                'brand': col4,
                'station_name': col5,
                'address': col6,
                'query1': query1,
                'query2': query2
            })

            # 添加查询任务
            query_tasks.append({
                'index': index,
                'query': query1,
                'type': 'query1'
            })
            query_tasks.append({
                'index': index,
                'query': query2,
                'type': 'query2'
            })

        print(f"\n开始并发查询，总共 {len(query_tasks)} 个查询任务，使用 {max_workers} 个并发线程...")

        # 执行并发查询
        query_results = {}
        completed_count = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_task = {executor.submit(self.geocode_single_query, task): task for task in query_tasks}

            # 收集结果
            for future in as_completed(future_to_task):
                try:
                    result = future.result()
                    index = result['index']
                    query_type = result['type']

                    if index not in query_results:
                        query_results[index] = {}

                    query_results[index][query_type] = result['result']

                    completed_count += 1
                    if completed_count % 100 == 0:
                        print(f"已完成 {completed_count}/{len(query_tasks)} 个查询...")

                except Exception as e:
                    print(f"查询任务失败: {e}")

        print(f"所有查询完成！开始计算距离误差...")

        # 组合结果
        results = []
        for row_info in row_data:
            index = row_info['index']

            result1 = query_results.get(index, {}).get('query1', {})
            result2 = query_results.get(index, {}).get('query2', {})

            # 计算距离误差
            distance = self.calculate_distance(
                result1.get('lat'), result1.get('lng'),
                result2.get('lat'), result2.get('lng')
            )

            result_data = {
                'row_index': index + 1,
                'county': row_info['county'],
                'district': row_info['district'],
                'brand': row_info['brand'],
                'station_name': row_info['station_name'],
                'address': row_info['address'],
                'query1': row_info['query1'],
                'query2': row_info['query2'],
                'result1_lat': result1.get('lat'),
                'result1_lng': result1.get('lng'),
                'result1_address': result1.get('formatted_address'),
                'result1_status': result1.get('status'),
                'result2_lat': result2.get('lat'),
                'result2_lng': result2.get('lng'),
                'result2_address': result2.get('formatted_address'),
                'result2_status': result2.get('status'),
                'distance_error_meters': distance if distance != float('inf') else None
            }

            results.append(result_data)

        return results
    
    def save_results(self, results: List[Dict], output_file: str = "coordinate_comparison_results.xlsx"):
        """保存结果到Excel文件"""
        if not results:
            print("没有结果可保存")
            return
        
        df_results = pd.DataFrame(results)
        
        # 添加统计信息
        valid_distances = [r for r in results if r['distance_error_meters'] is not None]
        
        if valid_distances:
            distances = [r['distance_error_meters'] for r in valid_distances]
            stats = {
                'total_queries': len(results),
                'successful_comparisons': len(valid_distances),
                'avg_distance_error': sum(distances) / len(distances),
                'max_distance_error': max(distances),
                'min_distance_error': min(distances),
                'median_distance_error': sorted(distances)[len(distances)//2]
            }
            
            print(f"\n=== 统计结果 ===")
            print(f"总查询数: {stats['total_queries']}")
            print(f"成功对比数: {stats['successful_comparisons']}")
            print(f"平均误差: {stats['avg_distance_error']:.2f} 米")
            print(f"最大误差: {stats['max_distance_error']:.2f} 米")
            print(f"最小误差: {stats['min_distance_error']:.2f} 米")
            print(f"中位数误差: {stats['median_distance_error']:.2f} 米")
        
        try:
            df_results.to_excel(output_file, index=False)
            print(f"\n结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存结果失败: {e}")

def main():
    # Google API密钥
    API_KEY = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"

    # Excel文件路径
    EXCEL_FILE = "全部縣市加油站位置.xlsx"

    # 创建查询器
    query_tool = GasStationCoordinateQuery(API_KEY)

    print("开始处理加油站坐标查询（100并发模式）...")
    print("=" * 50)

    start_time = time.time()

    # 并发处理Excel文件
    results = query_tool.process_excel_file_concurrent(EXCEL_FILE, max_workers=100)

    end_time = time.time()
    print(f"\n查询耗时: {end_time - start_time:.2f} 秒")

    # 保存结果
    if results:
        query_tool.save_results(results)
        print("\n处理完成！")
    else:
        print("\n处理失败，没有获得有效结果。")

if __name__ == "__main__":
    main()
