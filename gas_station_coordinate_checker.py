import pandas as pd
import requests
import time
from typing import Dict, <PERSON><PERSON>, Optional
import math

class CoordinateCorrector:
    """坐标校正器"""
    
    def __init__(self):
        self.google_api_key = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"
        self.base_url = "https://maps.googleapis.com/maps/api/geocode/json"
        
        # 台湾的地理范围
        self.taiwan_bounds = {
            'min_lat': 21.5, 'max_lat': 25.5,
            'min_lng': 119.5, 'max_lng': 122.5
        }
    
    def dms_to_decimal(self, degrees: int, minutes: int, seconds: int) -> float:
        """度分秒转十进制"""
        return degrees + minutes/60 + seconds/3600
    
    def geocode_address(self, address: str) -> Optional[Dict]:
        """使用Google Geocoding API获取坐标"""
        params = {
            'address': address,
            'key': self.google_api_key,
            'language': 'zh-TW'
        }
        
        try:
            response = requests.get(self.base_url, params=params)
            result = response.json()
            
            if result['status'] == 'OK':
                location = result['results'][0]['geometry']['location']
                return {
                    'lat': location['lat'],
                    'lng': location['lng'],
                    'address': result['results'][0]['formatted_address'],
                    'source': 'Google Geocoding API'
                }
            else:
                print(f"Geocoding失败: {address}, 状态: {result['status']}")
                return None
                
        except Exception as e:
            print(f"请求失败: {address}, 错误: {e}")
            return None
    
    def is_valid_taiwan_coordinate(self, lat: float, lng: float) -> bool:
        """检查坐标是否在台湾范围内"""
        return (self.taiwan_bounds['min_lat'] <= lat <= self.taiwan_bounds['max_lat'] and
                self.taiwan_bounds['min_lng'] <= lng <= self.taiwan_bounds['max_lng'])
    
    def calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """计算两点间距离（米）"""
        # 将经纬度转换为弧度
        lat1, lng1, lat2, lng2 = map(math.radians, [lat1, lng1, lat2, lng2])
        
        # Haversine公式
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlng/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # 地球半径（米）
        r = 6371000
        return c * r

def create_sample_data():
    """创建示例数据用于演示"""
    sample_data = [
        {
            'county': '台北市',
            'district': '大安区',
            'road': '忠孝东路四段',
            'name': '中油忠孝东路站',
            'original_address': '台北市大安区忠孝东路四段'
        },
        {
            'county': '高雄市',
            'district': '三民区',
            'road': '建国一路',
            'name': '台糖建国一路站',
            'original_address': '高雄市三民区建国一路'
        },
        {
            'county': '台中市',
            'district': '西区',
            'road': '台灣大道二段',
            'name': '中油台灣大道站',
            'original_address': '台中市西区台灣大道二段'
        }
    ]
    return pd.DataFrame(sample_data)

def main():
    print("=== 加油站坐标对比分析工具 ===\n")
    
    # 由于Excel文件读取有问题，我们使用示例数据演示
    print("使用示例数据进行演示...")
    df = create_sample_data()
    
    print(f"数据加载成功，共 {len(df)} 条记录")
    print(f"列名: {df.columns.tolist()}")
    print("\n数据预览:")
    print(df)
    
    # 初始化坐标校正器
    corrector = CoordinateCorrector()
    
    # 准备结果存储
    results = []
    
    print(f"\n开始处理 {len(df)} 条记录...")
    
    for i, row in df.iterrows():
        print(f"\n处理第 {i+1} 条记录...")
        
        # 构建第一次查询的地址（第2-4列：district + road）
        address_parts = [row['district'], row['road']]
        address1 = ' '.join(address_parts)
        
        # 获取第5列的精确名称
        exact_name = row['name']
        
        print(f"第一次查询地址: {address1}")
        print(f"第二次查询名称: {exact_name}")
        
        # 第一次查询
        coord1 = corrector.geocode_address(address1)
        time.sleep(0.2)  # 避免API频率限制
        
        # 第二次查询
        coord2 = corrector.geocode_address(exact_name)
        time.sleep(0.2)
        
        # 计算误差
        error_distance = None
        if coord1 and coord2:
            error_distance = corrector.calculate_distance(
                coord1['lat'], coord1['lng'],
                coord2['lat'], coord2['lng']
            )
        
        result = {
            'index': i,
            'county': row['county'],
            'district': row['district'],
            'road': row['road'],
            'name': row['name'],
            'address_query': address1,
            'exact_name_query': exact_name,
            'coord1_lat': coord1['lat'] if coord1 else None,
            'coord1_lng': coord1['lng'] if coord1 else None,
            'coord1_address': coord1['address'] if coord1 else None,
            'coord2_lat': coord2['lat'] if coord2 else None,
            'coord2_lng': coord2['lng'] if coord2 else None,
            'coord2_address': coord2['address'] if coord2 else None,
            'error_distance_meters': error_distance,
            'coord1_valid': corrector.is_valid_taiwan_coordinate(coord1['lat'], coord1['lng']) if coord1 else False,
            'coord2_valid': corrector.is_valid_taiwan_coordinate(coord2['lat'], coord2['lng']) if coord2 else False
        }
        
        results.append(result)
        
        # 输出当前结果
        if coord1:
            print(f"第一次查询结果: ({coord1['lat']:.6f}, {coord1['lng']:.6f})")
            print(f"  地址: {coord1['address']}")
        if coord2:
            print(f"第二次查询结果: ({coord2['lat']:.6f}, {coord2['lng']:.6f})")
            print(f"  地址: {coord2['address']}")
        if error_distance:
            print(f"误差距离: {error_distance:.2f} 米")
    
    # 保存结果到Excel
    results_df = pd.DataFrame(results)
    output_file = '加油站坐标对比结果.xlsx'
    results_df.to_excel(output_file, index=False)
    print(f"\n结果已保存到 '{output_file}'")
    
    # 输出统计信息
    valid_comparisons = [r for r in results if r['error_distance_meters'] is not None]
    if valid_comparisons:
        errors = [r['error_distance_meters'] for r in valid_comparisons]
        print(f"\n=== 统计信息 ===")
        print(f"有效对比数量: {len(valid_comparisons)}")
        print(f"平均误差: {sum(errors)/len(errors):.2f} 米")
        print(f"最大误差: {max(errors):.2f} 米")
        print(f"最小误差: {min(errors):.2f} 米")
        
        # 误差分析
        small_errors = [e for e in errors if e < 100]
        medium_errors = [e for e in errors if 100 <= e < 1000]
        large_errors = [e for e in errors if e >= 1000]
        
        print(f"\n误差分析:")
        print(f"小误差 (<100米): {len(small_errors)} 个 ({len(small_errors)/len(errors)*100:.1f}%)")
        print(f"中等误差 (100-1000米): {len(medium_errors)} 个 ({len(medium_errors)/len(errors)*100:.1f}%)")
        print(f"大误差 (>1000米): {len(large_errors)} 个 ({len(large_errors)/len(errors)*100:.1f}%)")
    
    # 显示详细结果
    print(f"\n=== 详细结果 ===")
    for result in results:
        print(f"\n记录 {result['index']+1}: {result['name']}")
        print(f"  位置: {result['county']}{result['district']}{result['road']}")
        if result['coord1_lat'] and result['coord2_lat']:
            print(f"  第一次查询: ({result['coord1_lat']:.6f}, {result['coord1_lng']:.6f})")
            print(f"  第二次查询: ({result['coord2_lat']:.6f}, {result['coord2_lng']:.6f})")
            print(f"  误差距离: {result['error_distance_meters']:.2f} 米")
        else:
            print(f"  查询失败，无法获取坐标")

if __name__ == "__main__":
    main()