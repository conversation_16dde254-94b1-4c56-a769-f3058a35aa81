import pandas as pd

def check_excel_structure(file_path):
    """检查Excel文件结构"""
    try:
        print(f"正在读取Excel文件: {file_path}")
        df = pd.read_excel(file_path)
        
        print(f"\n文件读取成功！")
        print(f"数据形状: {df.shape} (行数: {df.shape[0]}, 列数: {df.shape[1]})")
        
        print(f"\n列名:")
        for i, col in enumerate(df.columns):
            print(f"  第{i+1}列: {col}")
        
        print(f"\n前10行数据:")
        print(df.head(10))
        
        print(f"\n各列数据类型:")
        print(df.dtypes)
        
        print(f"\n各列非空值数量:")
        print(df.count())
        
        # 检查第2-5列的示例数据
        print(f"\n第2-5列示例数据:")
        for i in range(min(5, len(df))):
            row = df.iloc[i]
            print(f"第{i+1}行:")
            print(f"  第2列: {row.iloc[1] if len(row) > 1 else 'N/A'}")
            print(f"  第3列: {row.iloc[2] if len(row) > 2 else 'N/A'}")
            print(f"  第4列: {row.iloc[3] if len(row) > 3 else 'N/A'}")
            print(f"  第5列: {row.iloc[4] if len(row) > 4 else 'N/A'}")
            print()
        
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_excel_structure("全部縣市加油站位置.xlsx")
