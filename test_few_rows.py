import pandas as pd
import requests
import time
import json
import math
from typing import Dict, List, Tuple, Optional

class GasStationCoordinateQuery:
    """加油站坐标查询和对比工具（测试版本）"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://maps.googleapis.com/maps/api/geocode/json"
        
        # 台湾的地理范围
        self.taiwan_bounds = {
            'min_lat': 21.5, 'max_lat': 25.5,
            'min_lng': 119.5, 'max_lng': 122.5
        }
        
    def geocode_address(self, address: str, region: str = "tw") -> Optional[Dict]:
        """使用Google Geocoding API查询地址坐标"""
        params = {
            'address': address,
            'key': self.api_key,
            'language': 'zh-TW',
            'region': region
        }
        
        try:
            response = requests.get(self.base_url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data['status'] == 'OK' and data['results']:
                location = data['results'][0]['geometry']['location']
                return {
                    'lat': location['lat'],
                    'lng': location['lng'],
                    'formatted_address': data['results'][0]['formatted_address'],
                    'status': 'SUCCESS'
                }
            else:
                return {
                    'lat': None,
                    'lng': None,
                    'formatted_address': None,
                    'status': f"ERROR: {data['status']}"
                }
        except Exception as e:
            return {
                'lat': None,
                'lng': None,
                'formatted_address': None,
                'status': f"EXCEPTION: {str(e)}"
            }
    
    def calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """计算两点间的距离（米）"""
        if None in [lat1, lng1, lat2, lng2]:
            return float('inf')
            
        # 使用Haversine公式计算距离
        R = 6371000  # 地球半径（米）
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lng = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lng/2) * math.sin(delta_lng/2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def test_few_rows(self, file_path: str, max_rows: int = 3) -> List[Dict]:
        """测试前几行数据"""
        print(f"正在读取Excel文件: {file_path}")
        
        try:
            df = pd.read_excel(file_path)
            print(f"文件读取成功，共 {len(df)} 行数据")
            print(f"列名: {df.columns.tolist()}")
            
            # 只处理前max_rows行
            df = df.head(max_rows)
            print(f"\n测试模式：只处理前 {max_rows} 行数据")
            
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return []
        
        results = []
        total_rows = len(df)
        
        for index, row in df.iterrows():
            print(f"\n处理第 {index + 1}/{total_rows} 行数据...")
            
            # 获取各列数据
            col2 = str(row.iloc[1]) if pd.notna(row.iloc[1]) else ""  # 縣市
            col3 = str(row.iloc[2]) if pd.notna(row.iloc[2]) else ""  # 鄉鎮市區
            col4 = str(row.iloc[3]) if pd.notna(row.iloc[3]) else ""  # 品牌
            col5 = str(row.iloc[4]) if pd.notna(row.iloc[4]) else ""  # 加油站名稱
            col6 = str(row.iloc[5]) if pd.notna(row.iloc[5]) else ""  # 地址
            
            print(f"原始数据:")
            print(f"  縣市: {col2}")
            print(f"  鄉鎮市區: {col3}")
            print(f"  品牌: {col4}")
            print(f"  加油站名稱: {col5}")
            print(f"  地址: {col6}")
            
            # 第一次查询：组合第2-5列（縣市+鄉鎮市區+品牌+加油站名稱）
            query1 = f"{col2} {col3} {col4} {col5}".strip()
            print(f"\n第一次查询: '{query1}'")
            
            result1 = self.geocode_address(query1)
            time.sleep(0.5)  # 避免API限制
            
            # 第二次查询：第6列地址
            query2 = col6.strip()
            print(f"第二次查询: '{query2}'")
            
            result2 = self.geocode_address(query2)
            time.sleep(0.5)  # 避免API限制
            
            # 计算距离误差
            distance = self.calculate_distance(
                result1.get('lat'), result1.get('lng'),
                result2.get('lat'), result2.get('lng')
            )
            
            # 显示结果
            print(f"\n查询1结果: {result1.get('status')}")
            if result1.get('lat'):
                print(f"  坐标: {result1.get('lat')}, {result1.get('lng')}")
                print(f"  地址: {result1.get('formatted_address')}")
            
            print(f"查询2结果: {result2.get('status')}")
            if result2.get('lat'):
                print(f"  坐标: {result2.get('lat')}, {result2.get('lng')}")
                print(f"  地址: {result2.get('formatted_address')}")
            
            if distance != float('inf'):
                print(f"坐标误差: {distance:.2f} 米")
            else:
                print("坐标误差: 无法计算（缺少有效坐标）")
            
            result_data = {
                'row_index': index + 1,
                'county': col2,
                'district': col3,
                'brand': col4,
                'station_name': col5,
                'address': col6,
                'query1': query1,
                'query2': query2,
                'result1_lat': result1.get('lat'),
                'result1_lng': result1.get('lng'),
                'result1_address': result1.get('formatted_address'),
                'result1_status': result1.get('status'),
                'result2_lat': result2.get('lat'),
                'result2_lng': result2.get('lng'),
                'result2_address': result2.get('formatted_address'),
                'result2_status': result2.get('status'),
                'distance_error_meters': distance if distance != float('inf') else None
            }
            
            results.append(result_data)
        
        return results

def main():
    # Google API密钥
    API_KEY = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"
    
    # Excel文件路径
    EXCEL_FILE = "全部縣市加油站位置.xlsx"
    
    # 创建查询器
    query_tool = GasStationCoordinateQuery(API_KEY)
    
    print("开始测试加油站坐标查询...")
    print("=" * 50)
    
    # 测试前3行
    results = query_tool.test_few_rows(EXCEL_FILE, max_rows=3)
    
    if results:
        # 简单统计
        valid_distances = [r for r in results if r['distance_error_meters'] is not None]
        if valid_distances:
            distances = [r['distance_error_meters'] for r in valid_distances]
            print(f"\n=== 测试结果统计 ===")
            print(f"成功对比数: {len(valid_distances)}")
            if len(distances) > 0:
                print(f"平均误差: {sum(distances)/len(distances):.2f} 米")
                print(f"最大误差: {max(distances):.2f} 米")
                print(f"最小误差: {min(distances):.2f} 米")
        
        print("\n测试完成！")
    else:
        print("\n测试失败，没有获得有效结果。")

if __name__ == "__main__":
    main()
